Page({
  data: {
    // 当前选中的手机类型索引
    currentTypeIndex: 0,
    // 手机类型列表
    phoneTypes: ['靓机/小花', '花机/内爆', '卡贴外版', '外版无锁'],
    
    // 当前选择的型号
    selectedModel: '点击此处选择需要调整的型号',
    
    // 是否显示型号选择弹窗
    showModelSelector: false,
    
    // 可选择的手机型号列表
    phoneModels: [
      'iPhone 16', 'iPhone 16 Pro', 'iPhone 16 Pro Max',
      'iPhone 16 Plus', 'iPhone 15', 'iPhone 15 Plus',
      'iPhone 15 Pro', 'iPhone 15 Pro Max', 'iPhone 14',
      'iPhone 14 Plus', 'iPhone 14 Pro', 'iPhone 14 Pro Max',
      'iPhone 13 mini', 'iPhone 13', 'iPhone 13 Pro',
      'iPhone 13 Pro Max', 'iPhone 12 mini', 'iPhone 12',
      'iPhone 12 Pro', 'iPhone 12 Pro Max', 'iPhone 11',
      'iPhone 11 Pro', 'iPhone 11 Pro Max'
    ],
    
    // 当前选中的型号列表
    selectedModels: [],



    // 选中型号的配置数据
    selectedConfigs: [],

    // 型号配置数据模板
    modelConfigs: {
      'iPhone 16 Pro Max': {
        storages: [
          {
            capacity: '256G',
            prices: [
              { name: '高保靓机50次内存保280+', price: 7300 },
              { name: '靓机-单机100🈶️在保100+', price: 7200 },
              { name: '小充-单机100🈶️在保100+', price: 7000 },
              { name: '资源机20次内充新', price: 7200 }
            ]
          },
          {
            capacity: '512G',
            prices: [
              { name: '高保靓机50次内存保280+', price: 8450 },
              { name: '靓机-单机100🈶️在保100+', price: 8300 },
              { name: '小充-单机100🈶️在保100+', price: 8000 },
              { name: '资源机20次内充新', price: 8000 }
            ]
          },
          {
            capacity: '1T',
            prices: [
              { name: '高保靓机50次内存保280+', price: 9450 },
              { name: '靓机-单机100🈶️在保100+', price: 9300 }
            ]
          }
        ]
      }
    },

    // 价格配置数据
    priceConfigs: {
      '128G': [
        { name: '高保靓壳50次内在保280+', price: 6450, selected: false },
        { name: '靓机-单机100 在保100+', price: 5350, selected: false },
        { name: '小花-单机100 在保100+', price: 5150, selected: false },
        { name: '资源机20次内充新', price: 5400, selected: false }
      ],
      '256G': [
        { name: '高保靓壳50次内在保280+', price: 6500, selected: false },
        { name: '靓机-单机100 在保100+', price: 6400, selected: false },
        { name: '小花-单机100 在保100+', price: 6150, selected: false },
        { name: '资源机20次内充新', price: 6200, selected: false }
      ],
      '512G': [
        { name: '高保靓壳50次内在保280+', price: 7500, selected: false },
        { name: '靓机-单机100 在保100+', price: 7400, selected: false },
        { name: '小花-单机100 在保100+', price: 7100, selected: false },
        { name: '资源机20次内充新', price: 7200, selected: false }
      ],
      '1T': [
        { name: '高保靓壳50次内在保280+', price: 8500, selected: false },
        { name: '靓机-单机100 在保100+', price: 8400, selected: false },
        { name: '小花-单机100 在保100+', price: 8100, selected: false },
        { name: '资源机20次内充新', price: 7900, selected: false }
      ]
    },
    
    // 当前显示的容量
    currentCapacity: '128G',
    
    // 优势标记选项
    advantageOptions: [
      { id: 1, name: '价格优势', selected: false },
      { id: 2, name: '质量保证', selected: false },
      { id: 3, name: '售后服务', selected: false },
      { id: 4, name: '快速发货', selected: false },
      { id: 5, name: '正品保障', selected: false },
      { id: 6, name: '专业检测', selected: false }
    ]
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 切换手机类型
  switchPhoneType(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTypeIndex: index
    });
  },

  // 显示型号选择器
  toggleModelSelector() {
    this.setData({
      showModelSelector: !this.data.showModelSelector
    });
  },

  // 隐藏型号选择器
  hideModelSelector() {
    this.setData({
      showModelSelector: false
    });
  },

  // 防止弹窗关闭
  preventClose() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  // 清空选择
  clearSelection() {
    this.setData({
      selectedModels: [],
      selectedModel: '点击此处选择需要调整的型号',
      selectedConfigs: []
    });
  },

  // 确认选择
  confirmSelection() {
    if (this.data.selectedModels.length === 0) {
      wx.showToast({
        title: '请选择至少一个型号',
        icon: 'none'
      });
      return;
    }

    const displayText = this.data.selectedModels.length === 1
      ? this.data.selectedModels[0]
      : `${this.data.selectedModels[0]} 等${this.data.selectedModels.length}个型号`;

    this.setData({
      selectedModel: displayText,
      showModelSelector: false,
      selectedConfigs: this.generateConfigs(this.data.selectedModels)
    });
  },





  // 选择手机型号（多选）
  toggleModel(e) {
    const model = e.currentTarget.dataset.model;
    let selectedModels = [...this.data.selectedModels];

    // 多选逻辑：如果已选中则取消，未选中则添加
    const index = selectedModels.indexOf(model);
    if (index !== -1) {
      selectedModels.splice(index, 1);
    } else {
      selectedModels.push(model);
    }

    this.setData({
      selectedModels: selectedModels
    });

    console.log('选择型号:', model);
    console.log('当前selectedModels:', this.data.selectedModels);
  },

  // 生成选中型号的配置数据
  generateConfigs(selectedModels) {
    const configs = [];
    selectedModels.forEach(model => {
      if (this.data.modelConfigs[model]) {
        configs.push({
          model: model,
          storages: JSON.parse(JSON.stringify(this.data.modelConfigs[model].storages))
        });
      } else {
        // 如果没有预设配置，使用默认配置
        configs.push({
          model: model,
          storages: [
            {
              capacity: '256G',
              prices: [
                { name: '高保靓机50次内存保280+', price: 7000 },
                { name: '靓机-单机100🈶️在保100+', price: 6800 },
                { name: '小充-单机100🈶️在保100+', price: 6500 },
                { name: '资源机20次内充新', price: 6800 }
              ]
            }
          ]
        });
      }
    });
    return configs;
  },



  // 价格调整
  adjustPrice(e) {
    const { model, type } = e.currentTarget.dataset;
    const adjustment = type === 'plus' ? 50 : -50;
    const selectedConfigs = [...this.data.selectedConfigs];

    selectedConfigs.forEach(config => {
      if (config.model === model) {
        config.storages.forEach(storage => {
          storage.prices.forEach(price => {
            price.price += adjustment;
          });
        });
      }
    });

    this.setData({
      selectedConfigs: selectedConfigs
    });
  },

  // 更新价格
  updatePrice(e) {
    const { model, storage, name } = e.currentTarget.dataset;
    const newPrice = parseInt(e.detail.value) || 0;
    const selectedConfigs = [...this.data.selectedConfigs];

    selectedConfigs.forEach(config => {
      if (config.model === model) {
        config.storages.forEach(storageItem => {
          if (storageItem.capacity === storage) {
            storageItem.prices.forEach(price => {
              if (price.name === name) {
                price.price = newPrice;
              }
            });
          }
        });
      }
    });

    this.setData({
      selectedConfigs: selectedConfigs
    });
  },

  // 切换容量
  switchCapacity(e) {
    const capacity = e.currentTarget.dataset.capacity;
    this.setData({
      currentCapacity: capacity
    });
  },

  // 选择/取消选择价格配置
  togglePriceConfig(e) {
    const index = e.currentTarget.dataset.index;
    const capacity = this.data.currentCapacity;
    const configs = this.data.priceConfigs[capacity];
    
    configs[index].selected = !configs[index].selected;
    
    this.setData({
      [`priceConfigs.${capacity}`]: configs
    });
  },

  // 修改价格
  onPriceInput(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const capacity = this.data.currentCapacity;
    
    this.setData({
      [`priceConfigs.${capacity}[${index}].price`]: parseInt(value) || 0
    });
  },

  // 标记优势
  toggleAdvantage(e) {
    const index = e.currentTarget.dataset.index;
    const advantages = this.data.advantageOptions;
    
    advantages[index].selected = !advantages[index].selected;
    
    this.setData({
      advantageOptions: advantages
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 生成报价单
  generateQuote() {
    // 收集所有选中的配置
    const selectedConfigs = [];
    Object.keys(this.data.priceConfigs).forEach(capacity => {
      this.data.priceConfigs[capacity].forEach(config => {
        if (config.selected) {
          selectedConfigs.push({
            capacity: capacity,
            name: config.name,
            price: config.price
          });
        }
      });
    });

    const selectedAdvantages = this.data.advantageOptions.filter(item => item.selected);

    // 这里可以将数据传递给报价单页面或进行其他处理
    console.log('选中的配置:', selectedConfigs);
    console.log('选中的优势:', selectedAdvantages);

    wx.showToast({
      title: '报价单生成成功',
      icon: 'success'
    });

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },


});
