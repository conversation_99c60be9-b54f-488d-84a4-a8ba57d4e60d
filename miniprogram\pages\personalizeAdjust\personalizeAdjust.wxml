<view class="container">
  <!-- 手机类型切换 -->
  <view class="type-tabs">
    <view 
      class="type-tab {{currentTypeIndex === index ? 'active' : ''}}"
      wx:for="{{phoneTypes}}"
      wx:key="index"
      data-index="{{index}}"
      bindtap="switchPhoneType"
    >
      {{item}}
    </view>
  </view>

  <!-- 型号选择区域 -->
  <view class="model-section">
    <view class="model-label-row">
      <text class="model-label">选择型号：</text>
      <view class="selected-display" bindtap="toggleModelSelector">
        <text class="selected-text">{{selectedModel}}</text>
        <text class="dropdown-icon">▼</text>
      </view>
    </view>
  </view>



  <!-- 动态内容区域 -->
  <view class="content-section" wx:if="{{selectedModels.length > 0}}">
    <!-- 选中型号的配置信息 -->
    <view class="config-container" wx:for="{{selectedConfigs}}" wx:key="model">
      <view class="model-header">
        <text class="model-name">{{item.model}}</text>
        <view class="adjustment-controls">
          <button class="adjust-btn minus" data-model="{{item.model}}" data-type="minus" bindtap="adjustPrice">-50</button>
          <button class="adjust-btn plus" data-model="{{item.model}}" data-type="plus" bindtap="adjustPrice">+50</button>
          <text class="custom-label">自定义</text>
        </view>
      </view>

      <!-- 存储容量配置 -->
      <view class="storage-section" wx:for="{{item.storages}}" wx:for-item="storage" wx:key="capacity">
        <view class="storage-header">
          <text class="storage-capacity">{{storage.capacity}}</text>
        </view>
        <view class="price-list">
          <view class="price-item" wx:for="{{storage.prices}}" wx:for-item="priceItem" wx:key="name">
            <text class="price-name">{{priceItem.name}}</text>
            <input class="price-input" type="number" value="{{priceItem.price}}"
                   data-model="{{item.model}}"
                   data-storage="{{storage.capacity}}"
                   data-name="{{priceItem.name}}"
                   bindinput="updatePrice" />
            <text class="price-unit">标记优惠</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <view class="return-btn" bindtap="goBack">返回</view>
    <view class="generate-btn" bindtap="generateQuote">生成报价单</view>
  </view>
</view>
