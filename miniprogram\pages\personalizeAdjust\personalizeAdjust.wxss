page{
  background: #f5f5f5;
}

.container {
  background: #f5f5f5;
}
/* 手机类型切换 */
.type-tabs {
  display: flex;
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
  overflow: hidden;
}

.type-tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background: #f0f0f0;
  transition: all 0.3s;
}

.type-tab.active {
  background: #333;
  color: #fff;
}

/* 型号选择区域 */
.model-section {
  border-radius: 15rpx;
  padding: 10rpx 30rpx;
}

.model-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.model-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.selected-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5rpx 10rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  border: 1rpx solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 750rpx;
  overflow: hidden;
}

.selected-display:active {
  background: #e8e8e8;
}

.selected-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-icon {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

/* 型号选择器弹窗 */
.model-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-selector {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.selector-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background: #f0f0f0;
}

.model-grid {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.model-option {
  flex: 0 0 calc(33.333% - 14rpx);
  padding: 20rpx 15rpx;
  background: #f5f5f5;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  border-radius: 60rpx;
  transition: all 0.3s ease;
}

.model-option.selected {
  background: #333 !important;
  color: #fff !important;
}

.selector-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
  background: #f8f8f8;
}

.clear-btn {
  flex: 1;
  padding: 25rpx 0;
  text-align: center;
  background: #f0f0f0;
  color: #666;
  border-radius: 15rpx;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 2;
  padding: 25rpx 0;
  text-align: center;
  background: #333;
  color: #fff;
  border-radius: 15rpx;
  font-size: 28rpx;
}







.model-item {
  padding: 20rpx 15rpx;
  background: #f5f5f5;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  border-radius: 60rpx;
  flex: 0 0 calc(33.333% - 14rpx);
  margin-bottom: 20rpx;
}

.model-item-selected {
  padding: 20rpx 15rpx;
  background: black !important;
  color: white !important;
  text-align: center;
  font-size: 28rpx;
  white-space: nowrap;
  border-radius: 60rpx;
  flex: 0 0 calc(33.333% - 14rpx);
  margin-bottom: 20rpx;
}



/* 已选型号显示 */
.selected-models {
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
  padding: 20rpx;
}

.selected-models .model-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.selected-models .model-item:last-child {
  border-bottom: none;
}

.model-name {
  flex: 1;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.price-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.price-adjust {
  padding: 8rpx 15rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.advantage-mark {
  padding: 8rpx 15rpx;
  background: #333;
  color: #fff;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 价格列表 */
.price-list {
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
  overflow: hidden;
}

/* 容量切换 */
.capacity-tabs {
  display: flex;
  background: #f8f8f8;
  margin: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.capacity-tab {
  flex: 1;
  padding: 15rpx 0;
  text-align: center;
  font-size: 26rpx;
  color: #666;
  background: #f0f0f0;
}

.capacity-tab.active {
  background: #333;
  color: #fff;
}

/* 配置列表 */
.config-list {
  padding: 0 20rpx 20rpx;
}

.config-item {
  padding: 25rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.3s;
}

.config-item.selected {
  background: #f8f8ff;
}

.config-item:last-child {
  border-bottom: none;
}

.config-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.config-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.price-input {
  width: 120rpx;
  height: 50rpx;
  padding: 0 15rpx;
  background: #f8f8f8;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: center;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
}

.mark-btn {
  padding: 8rpx 15rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.return-btn {
  flex: 1;
  padding: 25rpx 0;
  text-align: center;
  background: #f0f0f0;
  color: #666;
  border-radius: 15rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.generate-btn {
  flex: 2;
  padding: 25rpx 0;
  text-align: center;
  background: #333;
  color: #fff;
  border-radius: 15rpx;
  font-size: 28rpx;
}



/* 动态内容区域 */
.content-section {
  margin: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 10rpx;
}

.config-container {
  margin-bottom: 30rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  overflow: hidden;
}

.model-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f8f8;
  border-bottom: 1rpx solid #e0e0e0;
}

.model-header .model-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.adjustment-controls {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.adjust-btn {
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
  color: #fff;
}

.adjust-btn.minus {
  background: #ff6b6b;
}

.adjust-btn.plus {
  background: #51cf66;
}

.custom-label {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.storage-section {
  margin-bottom: 20rpx;
}

.storage-header {
  padding: 15rpx 20rpx;
  background: #f0f0f0;
  text-align: center;
}

.storage-capacity {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-list {
  padding: 0 20rpx;
}

.price-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.price-item:last-child {
  border-bottom: none;
}

.price-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
}

.price-input {
  width: 120rpx;
  padding: 8rpx 12rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  text-align: center;
  font-size: 26rpx;
  margin-right: 10rpx;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
}
